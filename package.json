{"name": "tempt-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "tw-patch"}, "dependencies": {"@fontsource/cinzel": "^5.2.5", "@fontsource/instrument-sans": "^5.2.5", "@fontsource/inter": "^5.2.5", "@fontsource/lora": "^5.2.5", "@fontsource/uncial-antiqua": "^5.2.5", "@khanacademy/simple-markdown": "^0.14.0", "@next/font": "^14.2.15", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/themes": "^3.2.1", "@stitches/react": "^1.2.8", "@types/tinycolor2": "^1.4.6", "aceternity-ui": "^0.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "discord-markdown-parser": "^1.2.0", "framer-motion": "^12.5.0", "lucide-react": "^0.482.0", "next": "^15.2.2", "next-themes": "^0.4.5", "node": "^20.19.0", "numeral": "^2.0.6", "pg": "^8.14.0", "pretty-ms": "^9.2.0", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "recharts": "^2.15.1", "shadcn-ui": "^0.9.5", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tinycolor2": "^1.6.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/numeral": "^2.0.5", "@types/pg": "^8.11.11", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-patch": "^7.1.0", "typescript": "^5", "unplugin-tailwindcss-mangle": "^4.1.0"}}