@tailwind base;
@tailwind components;
@tailwind utilities;

@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Light.woff2") format("woff2"),
        url("/fonts/Satoshi-Light.woff") format("woff"),
        url("/fonts/Satoshi-Light.ttf") format("truetype");
    font-weight: 300;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-LightItalic.woff2") format("woff2"),
        url("/fonts/Satoshi-LightItalic.woff") format("woff"),
        url("/fonts/Satoshi-LightItalic.ttf") format("truetype");
    font-weight: 300;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Regular.woff2") format("woff2"),
        url("/fonts/Satoshi-Regular.woff") format("woff"),
        url("/fonts/Satoshi-Regular.ttf") format("truetype");
    font-weight: 400;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Italic.woff2") format("woff2"),
        url("/fonts/Satoshi-Italic.woff") format("woff"),
        url("/fonts/Satoshi-Italic.ttf") format("truetype");
    font-weight: 400;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Medium.woff2") format("woff2"),
        url("/fonts/Satoshi-Medium.woff") format("woff"),
        url("/fonts/Satoshi-Medium.ttf") format("truetype");
    font-weight: 500;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-MediumItalic.woff2") format("woff2"),
        url("/fonts/Satoshi-MediumItalic.woff") format("woff"),
        url("/fonts/Satoshi-MediumItalic.ttf") format("truetype");
    font-weight: 500;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Bold.woff2") format("woff2"),
        url("/fonts/Satoshi-Bold.woff") format("woff"),
        url("/fonts/Satoshi-Bold.ttf") format("truetype");
    font-weight: 700;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-BoldItalic.woff2") format("woff2"),
        url("/fonts/Satoshi-BoldItalic.woff") format("woff"),
        url("/fonts/Satoshi-BoldItalic.ttf") format("truetype");
    font-weight: 700;
    font-display: swap;
    font-style: italic;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Black.woff2") format("woff2"),
        url("/fonts/Satoshi-Black.woff") format("woff"),
        url("/fonts/Satoshi-Black.ttf") format("truetype");
    font-weight: 900;
    font-display: swap;
    font-style: normal;
}

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-BlackItalic.woff2") format("woff2"),
        url("/fonts/Satoshi-BlackItalic.woff") format("woff"),
        url("/fonts/Satoshi-BlackItalic.ttf") format("truetype");
    font-weight: 900;
    font-display: swap;
    font-style: italic;
}

/**
* This is a variable font
* You can control variable axes as shown below:
* font-variation-settings: wght 900.0;
*
* available axes:
'wght' (range from 300.0 to 900.0
*/
@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-Variable.woff2") format("woff2"),
        url("/fonts/Satoshi-Variable.woff") format("woff"),
        url("/fonts/Satoshi-Variable.ttf") format("truetype");
    font-weight: 300 900;
    font-display: swap;
    font-style: normal;
}

/**
* This is a variable font
* You can control variable axes as shown below:
* font-variation-settings: wght 900.0;
*
* available axes:
'wght' (range from 300.0 to 900.0
*/

@font-face {
    font-family: "Satoshi";
    src:
        url("/fonts/Satoshi-VariableItalic.woff2") format("woff2"),
        url("/fonts/Satoshi-VariableItalic.woff") format("woff"),
        url("/fonts/Satoshi-VariableItalic.ttf") format("truetype");
    font-weight: 300 900;
    font-display: swap;
    font-style: italic;
}

@layer base {
    html {
        font-family: "Satoshi";
    }
  :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }
  .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

.font-satoshi {
    font-family: "Satoshi";
}

:root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 23, 22, 22;
}

body {
    color: white;
    background: #000000;
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

body {
    overflow-x: hidden;
}

#gradient-canvas {
    position: fixed;
    z-index: -1;
    width: 100%;
    height: 100%;

    bottom: 0;
    left: 0;
    right: 0;
    top: 0;

    --gradient-color-1: #555555;
    --gradient-color-2: #000000;
    --gradient-color-3: #2b2a2a;
    --gradient-color-4: #000000;
}

::-webkit-scrollbar {
    width: 8px;
    background-color: #0c0d0d;
}

::-webkit-scrollbar-thumb {
    background-color: #ffffff;
    border-radius: 4px;
}

.footer {
    display: flex;
    width: 100dvw;
    padding-left: 25%;
    padding-right: 25%;
}

@media (max-width: 800px) {
    .footer {
        padding-left: 5%;
        padding-right: 5%;
    }
}

@layer utilities {
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }
    .no-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .touch-scroll {
        -webkit-overflow-scrolling: touch;
        touch-action: pan-x;
    }
}

@layer base {
  * {
    @apply border-border;
    }
  body {
    @apply bg-background text-foreground;
    }
}
